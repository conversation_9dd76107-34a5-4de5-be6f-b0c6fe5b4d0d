import React, { useState } from 'react';
import { useRequest, useUpdateEffect } from 'ahooks';
import { Pagination, Spin, Empty } from 'antd';
import PoiFilter from './components/PoiFilter';
import PoiStatistics from './components/PoiStatistics';
import PoiCard from './components/PoiCard';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import PoiListContext, { PoiQueryReq } from './context';
import NewSignAgentTaskProgress from '@src/components/NewSignAgentTaskProgress';
import './index.scss';

type PoiSearchRes = APISpec['/xianfu/api-v2/dove/data/poi/search']['response'];

const PoiList: React.FC = () => {
    const [filters, setFilters] = useState<PoiQueryReq>({
        page: 1,
        pageSize: 10,
    });
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 10,
    });
    const [openNewSignTaskDrawer, setOpenNewSignTaskDrawer] = useState(true);

    // 商家列表数据请求
    const {
        data: poiData,
        loading: poiLoading,
        run: fetchPoiData,
    } = useRequest(
        async (extra = {}) => {
            try {
                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/search',
                    {
                        ...filters,
                        ...pagination,
                        ...extra,
                    },
                );
                if (res?.code === 0 && res.data) {
                    return res.data as PoiSearchRes;
                }
                // 如果接口返回错误，抛出异常
                throw new Error(res?.msg || '获取商家列表失败');
            } catch (error) {
                console.error('搜索商家列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    // 处理筛选条件变化
    const handleFilterChange = (newFilters: Partial<PoiQueryReq>) => {
        setFilters(prev => ({ ...prev, page: 1, ...newFilters }));
        // 筛选条件变化时回到第一页
        setPagination(p => ({ ...p, page: 1 }));
    };

    useUpdateEffect(() => {
        fetchPoiData();
    }, [filters]);

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        setPagination({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
        fetchPoiData({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    return (
        <div className="poi-list">
            <div className="poi-list__filter">
                <PoiFilter onChange={handleFilterChange} />
            </div>
            <PoiListContext.Provider value={{ filters }}>
                <div className="poi-list__statistics poi-statistics">
                    <PoiStatistics />
                </div>
                <div className="poi-card-list">
                    {poiData?.data?.length ? (
                        <Spin spinning={poiLoading}>
                            {poiData.data?.map(poi => (
                                <PoiCard poi={poi} />
                            ))}
                        </Spin>
                    ) : (
                        <Empty
                            description="暂无商家数据"
                            image={Empty.PRESENTED_IMAGE_SIMPLE}
                        />
                    )}
                </div>
            </PoiListContext.Provider>
            {poiData?.total && poiData.total > 0 ? (
                <div className="poi-list__pagination">
                    <Pagination
                        current={pagination.page}
                        pageSize={pagination.pageSize}
                        total={poiData.total}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                        }
                        onChange={handlePaginationChange}
                        onShowSizeChange={handlePaginationChange}
                    />
                </div>
            ) : null}
            <NewSignAgentTaskProgress
                visible={openNewSignTaskDrawer}
                onClose={() => setOpenNewSignTaskDrawer(false)}
                wdcId="xx"
                agentTaskInfo={{}}
            />
        </div>
    );
};

export default PoiList;
