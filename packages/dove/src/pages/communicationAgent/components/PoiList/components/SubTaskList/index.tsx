import { useState, useContext } from 'react';
import { Tag, Button, Typography, Empty, Spin } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { useRequest, useDeepCompareEffect } from 'ahooks';
import dayjs from 'dayjs';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import PoiListContext from '../../context';
import CallLogBtn from '@src/pages/communicationAgent/components/TaskStatistic/TaskListTab/TaskList2/CallLogBtn';
import TakeoverBtn from '@src/pages/communicationAgent/components/TaskStatistic/TaskListTab/TaskList2/TakeoverBtn';
import TaskTrackBtn from '@src/pages/communicationAgent/components/TaskStatistic/TaskListTab/TaskList2/TaskTrackBtn';
import TaskReachDetail from '@src/components/TaskReachDetail';
import NewSignAgentTaskProgress from '@src/components/NewSignAgentTaskProgress';
import { TaskReachDetailParams } from '@src/components/TaskReachDetail/types';
import { ConcatTypeEnum } from '@src/constants';
import './index.scss';

const { Text } = Typography;

type SubTaskListResponse =
    APISpec['/xianfu/api-v2/dove/data/poi/subtask']['response'];

const SubTaskList = ({ poi }) => {
    const { filters } = useContext(PoiListContext);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 3,
    });
    const [allSubtasks, setAllSubtasks] = useState<any[]>([]);
    const [totalCount, setTotalCount] = useState(0);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [drawerParams, setDrawerParams] = useState<TaskReachDetailParams>({});
    const [openNewSignTaskDrawer, setOpenNewSignTaskDrawer] = useState(false);

    // 获取子任务列表
    const { loading, run: fetchSubtasks } = useRequest(
        async (isLoadMore = false) => {
            try {
                const requestData = {
                    poiId: poi.id,
                    poiType: poi.poiType,
                    page: isLoadMore ? pagination.page : 1,
                    pageSize: pagination.pageSize,
                    subtaskFilter: filters?.subtaskFilter,
                };

                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask',
                    requestData,
                );
                if (res.code === 0 && res.data) {
                    const responseData = res.data as SubTaskListResponse;
                    if (isLoadMore) {
                        // 加载更多时，追加数据
                        setAllSubtasks(prev => [
                            ...prev,
                            ...(responseData.data || []),
                        ]);
                    } else {
                        // 首次加载或刷新时，替换数据
                        setAllSubtasks(responseData.data || []);
                        setTotalCount(responseData.total || 0);
                    }
                    return responseData;
                } else {
                    throw new Error(res?.msg || '获取子任务列表失败');
                }
            } catch (error) {
                console.error('获取子任务列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    useDeepCompareEffect(() => {
        if (!filters?.subtaskFilter) return;
        // 重置状态并获取第一页数据
        setPagination({ page: 1, pageSize: 3 });
        fetchSubtasks(false);
    }, [filters?.subtaskFilter, poi?.poiId]);

    // 处理展开更多
    const handleLoadMore = () => {
        const nextPage = pagination.page + 1;
        setPagination(prev => ({
            ...prev,
            page: nextPage,
        }));
        fetchSubtasks(true);
    };

    // 处理普通任务 查看沟通记录
    const handleViewCommunicationRecord = task => {
        setDrawerVisible(true);
        setDrawerParams({
            audioUrl: task?.audioUrl,
            contactId: String(task.id),
            talkingSeconds: task.talkingTimeLen / 1000,
            contactContent: task?.contactContent,
            contactImages: task?.contactImages,
            massSendTitle: task?.massSendTitle,
            contactType: task.contactTypeId,
        });
    };

    // 获取任务状态文本
    const getStatusText = (status?: string) => {
        switch (status) {
            case 'COMPLETED':
                return '已完成';
            case 'IN_PROGRESS':
                return '进行中';
            case 'PENDING':
                return '待执行';
            case 'FAILED':
                return '失败';
            default:
                return status || '未知';
        }
    };

    if (!allSubtasks || allSubtasks.length === 0) {
        return (
            <div className="subtask-list__empty">
                <Empty
                    description="暂无子任务"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    const handleTaskBaseInfo = task => {
        return [
            {
                label: '创建人',
                value: task.creator,
            },
            {
                label: '创建时间',
                value: task.createTime
                    ? dayjs(task.createTime).format('YYYY-MM-DD HH:mm')
                    : '-',
            },
            {
                label: '执行状态',
                value: getStatusText(task.status),
            },
            {
                label: '关联Agent',
                value: task.agentName
                    ? `${task.agentName}（${task.agentId}）`
                    : '',
            },
            {
                label: '触达方式',
                value: task.contactType,
            },
            {
                label: '调度流程',
                value: task.processName,
            },
        ].filter(item => item.value);
    };

    const handleTaskImportantInfo = task => {
        return [
            {
                label: '触达结果',
                value: task.reachStatus,
            },
            {
                label: '失败原因',
                value: task.failureReason,
            },
            {
                label: '触达时间',
                value: task.reachTime
                    ? dayjs(task.reachTime).format('YYYY-MM-DD HH:mm')
                    : '-',
            },
        ].filter(item => item.value);
    };

    const renderTag = ({ followMis }) => {
        const AI = {
            backgroundImage:
                'linear-gradient(109deg, #625CFF 0%, #AB5CFF 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            border: 'none',
        };
        return (
            <Tag
                style={{
                    background: followMis
                        ? 'rgb(255, 240, 229)'
                        : 'rgb(237, 236, 255)',
                    color: '#FF6A00',
                    border: 'none',
                }}
            >
                <span style={!followMis ? AI : {}}>
                    {followMis ? 'BD跟进中' : 'AI跟进中'}
                </span>
            </Tag>
        );
    };

    const getTaskActions = task => {
        const record = {
            ...task,
            contactId: task?.id, // 任务ID
            objectId: poi?.id, // 门店ID
            objectType: poi.poiType, // 门店类型
        };

        //普通任务（外呼、IM
        const TaskActions = [
            {
                label: '沟通记录',
                onClick: handleViewCommunicationRecord,
            },
        ];

        //调度任务
        const AgentTaskActions = [
            {
                label: '沟通记录',
                children: <CallLogBtn record={record} />,
            },
            {
                label: '任务轨迹',
                children: <TaskTrackBtn record={record} />,
            },
            {
                label: '接管',
                children: (
                    <TakeoverBtn
                        record={record}
                        onDone={() => {
                            fetchSubtasks(); // 刷新crud
                        }}
                    />
                ),
            },
        ];
        return task?.contactTypeId === ConcatTypeEnum.AGENT_CALL
            ? task?.newSignTask
                ? AgentTaskActions.push({
                      label: 'AI跟进进度',
                      onClick: () => {
                          console.log('新签AI跟进进度');
                      },
                  })
                : AgentTaskActions
            : TaskActions;
    };

    return (
        <div className="subtask-list">
            <div className="subtask-list__content">
                <Spin spinning={loading}>
                    {allSubtasks.map((task, index) => (
                        <div key={task.id || index} className="subtask-item">
                            <div className="subtask-item__header">
                                <div className="subtask-item__title">
                                    <Text
                                        strong
                                        ellipsis={{
                                            tooltip: `${task.taskName}  ID：${task.id}`,
                                        }}
                                    >
                                        {`${task.taskName}  ID：${task.id}`}
                                    </Text>
                                </div>
                                {renderTag(task)}
                            </div>

                            <div className="subtask-item__content">
                                {/* 任务类型信息行 */}
                                <div className="subtask-item__task-info">
                                    {handleTaskBaseInfo(task).map(
                                        (item, index) => (
                                            <Text
                                                key={index}
                                                type="secondary"
                                                style={{ fontSize: '12px' }}
                                            >
                                                {item.label}：{item.value}
                                            </Text>
                                        ),
                                    )}
                                </div>
                                <div className="subtask-item__task-info important">
                                    {handleTaskImportantInfo(task).map(
                                        (item, index) => (
                                            <Text
                                                key={index}
                                                type="secondary"
                                                style={{ fontSize: '12px' }}
                                            >
                                                {item.label}：{item.value}
                                            </Text>
                                        ),
                                    )}
                                </div>
                                {/* AI总结 */}
                                {!task.summary && (
                                    <div className="subtask-item__summary">
                                        <Text
                                            type="secondary"
                                            style={{
                                                fontSize: '12px',
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            AI总结：
                                        </Text>
                                        查看沟通记录查看沟通记录{task.summary}
                                    </div>
                                )}
                                {/* 操作按钮 */}
                                <div className="subtask-item__actions">
                                    {getTaskActions(task)?.map(action => {
                                        return (
                                            action?.children || (
                                                <Button
                                                    type="link"
                                                    size="small"
                                                    style={{
                                                        padding: 0,
                                                        fontSize: '12px',
                                                    }}
                                                    onClick={() =>
                                                        action.onClick(task)
                                                    }
                                                >
                                                    {action.label}
                                                </Button>
                                            )
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    ))}
                </Spin>
            </div>
            {allSubtasks.length < totalCount && (
                <div className="subtask-list__footer">
                    <Button
                        type="link"
                        size="small"
                        onClick={handleLoadMore}
                        loading={loading}
                        className="subtask-list__load-more"
                    >
                        展开更多
                        <DownOutlined />
                    </Button>
                </div>
            )}
            <TaskReachDetail
                key={drawerParams.contactId}
                visible={drawerVisible}
                textPath={'/xianfu/api/dove/data/text'}
                onClose={() => {
                    setDrawerVisible(false);
                    setDrawerParams({});
                }}
                {...drawerParams}
            />
            {/* <NewSignAgentTaskProgress
                visible={openNewSignTaskDrawer}
                onClose={() => setOpenNewSignTaskDrawer(false)}
                leadId="23"
            /> */}
        </div>
    );
};

export default SubTaskList;
