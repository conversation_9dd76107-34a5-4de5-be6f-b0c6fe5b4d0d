import React, { useState, useEffect, useMemo } from 'react';
import { Form, Input, Select, DatePicker, Button, Space, Row, Col } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import MisSelect from '@src/components/MisSelector';
import SelectAgent from '@src/components/SelectAgent';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import ContactTypeSelect from '@src/components/ContactTypeSelect';
import type { PoiFilterProps, FilterFormData } from '../../types';
import useObjectType from '@src/hooks/useObjectType';
import { taskStatusOptions } from '@src/constants';

const PoiFilter: React.FC<PoiFilterProps> = ({ onChange }) => {
    const { data: objectTypeEnum } = useObjectType();
    const [form] = Form.useForm<FilterFormData>();
    const [expanded, setExpanded] = useState(false);

    // 初始化表单值
    useEffect(() => {
        const filters = transformFormDataToFilters(initValues);
        onChange(filters);
    }, []);

    // 处理搜索
    const handleSearch = () => {
        const values = form.getFieldsValue();
        const filters = transformFormDataToFilters(values);
        onChange(filters);
    };

    // 处理重置
    const handleReset = () => {
        form.resetFields();
        const filters = transformFormDataToFilters(initValues);
        onChange(filters);
    };

    const convertTimeRangeToTimestamp = (timeRange: any[]) => {
        if (timeRange?.length === 2) {
            return {
                min: dayjs(timeRange[0]).valueOf(),
                max: dayjs(timeRange[1]).valueOf(),
            };
        }
        return null;
    };

    // 转换表单数据为筛选条件
    const transformFormDataToFilters = (formData: FilterFormData) => {
        const {
            subtaskFilter: { createTime = [], subTaskStartTime = [] },
        } = formData;

        const createTimeRange = convertTimeRangeToTimestamp(createTime);
        if (createTimeRange) {
            formData.subtaskFilter.createTimeMin = createTimeRange.min;
            formData.subtaskFilter.createTimeMax = createTimeRange.max;
        }

        const startTimeRange = convertTimeRangeToTimestamp(subTaskStartTime);
        if (startTimeRange) {
            formData.subtaskFilter.subTaskStartTimeMin = startTimeRange.min;
            formData.subtaskFilter.subTaskStartTimeMax = startTimeRange.max;
        }

        return formData;
    };

    // 切换展开/收起
    const toggleExpanded = () => {
        setExpanded(!expanded);
    };

    const initValues: FilterFormData = {
        subtaskFilter: {
            createTime: [dayjs().subtract(30, 'day'), dayjs()],
            bizId: 5001, //外卖白领
        },
    };

    // 基础表单项（默认显示）
    const basicFormItems = [
        {
            name: 'poiId',
            label: '商家ID',
            children: <Input placeholder="请输入商家ID" allowClear />,
        },
        {
            name: 'poiName',
            label: '商家名称',
            children: <Input placeholder="请输入商家名称" allowClear />,
        },
        {
            name: 'assigneeList',
            label: '门店责任人',
            children: (
                <MisSelect
                    mis={false}
                    mode="multiple"
                    placeholder="请选择责任人"
                    allowClear
                />
            ),
        },
        {
            name: ['subtaskFilter', 'createTime'],
            label: '任务创建时间',
            children: (
                <DatePicker.RangePicker
                    showTime
                    format="YYYY-MM-DD"
                    placeholder={['开始时间', '结束时间']}
                    style={{ width: '100%' }}
                />
            ),
        },
        {
            name: ['subtaskFilter', 'taskId'],
            label: '任务ID',
            children: <Input placeholder="请输入任务ID" allowClear />,
        },
        {
            name: ['subtaskFilter', 'taskName'],
            label: '任务名称',
            children: <Input placeholder="请输入任务名称" allowClear />,
        },
        {
            name: ['subtaskFilter', 'creatorMis'],
            label: '任务创建人',
            children: (
                <MisSelect
                    mis={false}
                    mode={undefined}
                    placeholder="请选择创建人"
                    allowClear
                />
            ),
        },
    ];

    // 展开表单项
    const expandedFormItems = [
        {
            name: 'poiType',
            label: '商家类型',
            children: (
                <Select
                    placeholder="请选择商家类型"
                    options={objectTypeEnum?.map?.(v => ({
                        label: v.name,
                        value: v.objectType,
                    }))}
                />
            ),
        },
        {
            name: 'cityId',
            label: '所属城市',
            children: <Input placeholder="请输入城市ID" allowClear />,
        },
        {
            name: ['subtaskFilter', 'agentIdList'],
            label: 'AgentID',
            children: (
                <SelectAgent
                    mode="multiple"
                    placeholder="请选择Agent"
                    allowClear
                />
            ),
        },
        {
            name: ['subtaskFilter', 'agentName'],
            label: 'Agent名称',
            children: <Input placeholder="请输入Agent名称" allowClear />,
        },
        {
            name: ['subtaskFilter', 'contactType'],
            label: '触达方式',
            children: <ContactTypeSelect />,
        },
        {
            name: ['subtaskFilter', 'subTaskStatus'],
            label: '任务状态',
            children: (
                <Select
                    placeholder="请选择任务状态"
                    allowClear
                    options={taskStatusOptions}
                />
            ),
        },
        {
            name: ['subtaskFilter', 'subTaskStartTime'],
            label: '触达时间',
            children: (
                <DatePicker.RangePicker
                    showTime
                    format="YYYY-MM-DD"
                    placeholder={['开始时间', '结束时间']}
                    style={{ width: '100%' }}
                />
            ),
        },
        {
            name: ['subtaskFilter', 'bizId'],
            label: '业务线/租户',
            children: (
                <TeamSelect
                    mode={undefined}
                    placeholder="请选择业务线"
                    allowClear
                    optionPath="/xianfu/api-v2/dove/staff/biz/query"
                />
            ),
        },
    ];

    const formItems = useMemo(() => {
        return expanded
            ? [...basicFormItems, ...expandedFormItems]
            : basicFormItems;
    }, [expanded]);

    return (
        <Form
            initialValues={initValues}
            form={form}
            layout="horizontal"
            onFinish={handleSearch}
        >
            {/* 基础表单项 */}
            <Row gutter={16} style={{ margin: 0 }}>
                {formItems.map((item, index) => (
                    <Col span={6} key={index}>
                        <Form.Item name={item.name} label={item.label}>
                            {item.children}
                        </Form.Item>
                    </Col>
                ))}
            </Row>
            {/* 操作按钮 */}
            <div className="poi-list__filter__opeation">
                <Space>
                    <Button type="link" onClick={toggleExpanded}>
                        {expanded ? '收起' : '展开'}
                        {expanded ? <UpOutlined /> : <DownOutlined />}
                    </Button>
                    <Button onClick={handleReset}>重置</Button>
                    <Button type="primary" onClick={handleSearch}>
                        查询
                    </Button>
                </Space>
            </div>
        </Form>
    );
};

export default PoiFilter;
