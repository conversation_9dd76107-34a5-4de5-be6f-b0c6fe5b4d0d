import { useState, useEffect } from 'react';
import { getLlmOptions, getVoiceOptions } from '../../../../services';

export const useLLM = ({ submittedTenantId }) => {
    const [llmOptions, setLlmOptions] = useState<any[]>([]);
    const [voiceOptions, setVoiceOptions] = useState<any[]>([]);

    // 监听submittedTenantId变化，获取依赖租户ID的数据
    useEffect(() => {
        // 只有当提交的租户ID有值时才获取依赖的数据
        if (submittedTenantId) {
            const fetchDependentData = async () => {
                try {
                    // 并行获取依赖租户ID的数据
                    const [llmRes, voiceRes] = await Promise.all([
                        getLlmOptions({ jupiterTenantId: submittedTenantId }),
                        getVoiceOptions({ jupiterTenantId: submittedTenantId }),
                    ]);

                    // 设置大模型和音色选项数据
                    setLlmOptions(llmRes || []);
                    setVoiceOptions(voiceRes || []);
                } catch (error) {
                    console.error('获取大模型和音色数据失败:', error);
                }
            };

            fetchDependentData();
        } else {
            // 当提交的租户ID为空时，清空依赖的数据
            setLlmOptions([]);
            setVoiceOptions([]);
        }
    }, [submittedTenantId]);

    return {
        llmOptions,
        voiceOptions,
    };
};

export const useLlmAppId = ({ llmOptions, selectedLlmName }) => {
    const [showIntentionAppId, setShowIntentionAppId] = useState(true);

    useEffect(() => {
        if (selectedLlmName && llmOptions.length > 0) {
            const selectedModel = llmOptions.find(
                model => model.id === selectedLlmName,
            );
            // 如果模型包含isFree字段且为true，则隐藏计费APPID
            setShowIntentionAppId(
                !(selectedModel && selectedModel.isFree === true),
            );
        } else {
            setShowIntentionAppId(true);
        }
    }, [selectedLlmName, llmOptions]);

    return [showIntentionAppId];
};
