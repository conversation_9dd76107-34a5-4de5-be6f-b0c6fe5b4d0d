import React from 'react';
import { FormPro, Input, Selector } from '@roo/roo';
import { Checkbox, Input as AntInput } from 'antd';
import IntentionLabelContainer, {
    handleAllowFrontLineConfigChange,
} from '../../../../common/components/IntentionLabelContainer';
import { useLlmAppId } from '../hooks/useLLM';

interface IntentionConfigProps {
    form: any;
    llmOptions: any[];
    submittedTenantId: string;
    isLoadingLlm: boolean;
}

const IntentionConfig: React.FC<IntentionConfigProps> = ({
    form,
    llmOptions,
    submittedTenantId,
    isLoadingLlm,
}) => {
    // 监听意向标签的allowFrontLineChange变化
    const intentionAllowFrontLineChange = FormPro.useWatch(
        ['intention', 'allowFrontLineChange'],
        form,
    );

    const selectedIntentionLlmName = FormPro.useWatch(
        ['intention', 'llmName'],
        form,
    );
    // 添加状态来跟踪是否显示意向标签计费APPID
    const [showIntentionLlmAppId] = useLlmAppId({
        llmOptions,
        selectedLlmName: selectedIntentionLlmName,
    });

    // 处理意向标签allowFrontLineChange变化
    const onIntentionAllowFrontLineChange = (checked: boolean) => {
        handleAllowFrontLineConfigChange(checked, form, [
            'intention',
            'elementList',
        ]);
    };

    return (
        <div className="intention-label-config basic-config">
            <FormPro.Item
                name={['intention', 'allowFrontLineChange']}
                valuePropName="checked"
                label=" "
                colon={false}
                initialValue={false}
            >
                <Checkbox
                    onChange={e =>
                        onIntentionAllowFrontLineChange(e.target.value)
                    }
                >
                    <span>一线可修改</span>
                </Checkbox>
            </FormPro.Item>
            <FormPro.Item
                label="大模型"
                name={['intention', 'llmName']}
                rules={[{ required: true, message: '请选择模型' }]}
                extra={
                    !submittedTenantId ? '请先填写并离开木星租户ID输入框' : null
                }
            >
                <Selector
                    fieldNames={{ label: 'name', value: 'id' }}
                    placeholder={
                        !submittedTenantId
                            ? '请先填写并离开木星租户ID输入框'
                            : '请选择模型'
                    }
                    options={llmOptions}
                    style={{ width: '100%' }}
                    disabled={!submittedTenantId || isLoadingLlm}
                />
            </FormPro.Item>
            {showIntentionLlmAppId && (
                <FormPro.Item
                    label="计费APPID"
                    name={['intention', 'llmAppId']}
                    rules={[
                        {
                            required: showIntentionLlmAppId,
                            message: '请输入计费APPID',
                        },
                    ]}
                >
                    <Input placeholder="请输入计费APPID"></Input>
                </FormPro.Item>
            )}
            <FormPro.Item
                label="Prompt"
                name={['intention', 'systemPrompt']}
                rules={[{ required: true, message: '请输入prompt' }]}
            >
                <AntInput.TextArea style={{ minHeight: 300 }} />
            </FormPro.Item>
            <FormPro.Item
                label="意向标签配置"
                rules={[
                    {
                        required: true,
                        message: '请选择意向标签配置',
                    },
                ]}
            >
                <IntentionLabelContainer
                    name={['intention', 'elementList']}
                    form={form}
                    allowBDChangeConfig={intentionAllowFrontLineChange}
                    showAllowBDEdit={true}
                />
            </FormPro.Item>
        </div>
    );
};

export default IntentionConfig;
