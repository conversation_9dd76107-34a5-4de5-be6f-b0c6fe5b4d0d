import React, { useEffect, useState } from 'react';
import {
    Table,
    Button,
    Tag,
    Tooltip,
    Popover,
    Typography,
    Input,
    Space,
    message,
} from 'antd';
import dayjs from 'dayjs';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import {
    reachMethodOptions,
    executionStatusOptions,
    communicationResultOptions,
    reachSystemMap,
    reachSystemoptions,
    CommunicationResultEnum,
    ReleaseReasonText,
} from '@src/constants';
import { useAntdTable, useRequest } from 'ahooks';
import { ColumnType } from 'antd/es/table';
import { QuestionCircleOutlined } from '@ant-design/icons';
import TaskReachDetail from '@src/components/TaskReachDetail';
import { TaskReachDetailParams } from '@src/components/TaskReachDetail/types';

type DataType =
    APISpec['/xianfu/api-v2/dove/data/query']['response']['data'][number];

interface TableListProps {
    searchParams?: {
        bizId?: number[];
        orgId?: number[];
        taskId?: number[];
        staffId?: number;
        contactObjectType?: number[];
        submitTimeMin?: number;
        submitTimeMax?: number;
        contactType?: number[];
        reachStatus?: number[];
        startTimeMin?: number[];
        startTimeMax?: number;
        executionStatus?: number[];
        rank?: string[];
        contactId?: number[];
    };
    onRowSelect?: (record: React.Key[]) => void;
    showPartColumns?: boolean;
    rankFilters?: {
        text: string;
        value: string;
    }[];
}

const TableList: React.FC<TableListProps> = ({
    searchParams = {},
    onRowSelect,
    showPartColumns,
    rankFilters,
}) => {
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const [drawerParams, setDrawerParams] = useState<TaskReachDetailParams>({});

    const getTableData = async ({
        current,
        pageSize,
        filters,
    }: {
        current: number;
        pageSize: number;
        filters: any;
    }) => {
        try {
            const taskId =
                searchParams.taskId ||
                new URLSearchParams(window.location.search).get('id');

            const res = await apiCaller.post('/xianfu/api-v2/dove/data/query', {
                ...searchParams,
                rank: filters?.rankDesc,
                reachStatus: filters?.reachStatus?.length
                    ? filters.reachStatus
                    : searchParams.reachStatus,
                page: current,
                pageSize,
                taskId: taskId
                    ? Array.isArray(taskId)
                        ? taskId
                        : [Number(taskId)]
                    : undefined,
                contactId: searchParams?.contactId || filters?.contactId, //沟通记录ID,兼容两处输入
                objectNameOrId: filters?.objectName?.join(''), //商家名称筛选
            } as any);

            if (res.code !== 0) {
                return {
                    list: [],
                    total: 0,
                };
            }

            const list = res.data.data.map((item: any) => ({
                ...item,

                creator: {
                    uid: item.creator?.uid || 0,
                    name: item.creator?.name || '-',
                },
            }));

            return {
                list,
                total: res.data.total,
            };
        } catch (error) {
            console.error('获取数据失败:', error);
            return {
                list: [],
                total: 0,
            };
        }
    };

    const { tableProps, search } = useAntdTable(getTableData, {
        defaultPageSize: 10,
        refreshDeps: [searchParams],
    });

    const fetchIntentionRecognize = useRequest(
        async (doveContactId: number) => {
            try {
                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/agent/intention',
                    {
                        doveContactId,
                    },
                );
                if (res.code === 0) {
                    message.success('意向已重新输出');
                    search.reset();
                    return;
                }
            } catch (error) {
                console.error('重置失败:', error);
            }
        },
        {
            manual: true,
            loadingDelay: 300,
            onBefore: () => {
                message.loading({
                    content: '意向重新输出中...',
                    key: 'resetIntention',
                    duration: 0, // 不自动关闭
                });
            },
            onFinally: () => {
                message.destroy('resetIntention');
            },
        },
    );

    useEffect(() => {
        search.reset();
    }, [searchParams.taskId]);

    const operateColumn: ColumnType<DataType> = {
        title: '沟通内容',
        dataIndex: 'audio',
        key: 'audio',
        fixed: 'right',
        render: (audio: string, record: DataType) => {
            return (
                <Button
                    type="link"
                    onClick={() => {
                        setDrawerVisible(true);
                        setDrawerParams({
                            audioUrl: audio,
                            contactId: String(record.contactId),
                            talkingSeconds: record.talkingTimeLen / 1000,
                            contactContent: record.contactContent,
                            contactImages: record.contactImages,
                            massSendTitle: record.massSendTitle,
                            contactType: record.contactType,
                        });
                    }}
                >
                    详情
                </Button>
            );
        },
    };
    const columns: (ColumnType<DataType> & {
        displayField?: Array<'simple' | 'full'>;
    })[] = [
        {
            title: '沟通记录ID',
            dataIndex: 'contactId',
            width: 200,
            filterDropdown: ({
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
                filters,
            }) => (
                <div style={{ padding: 8 }}>
                    <Input
                        placeholder="搜索沟通记录ID"
                        value={selectedKeys[0]}
                        onChange={e => {
                            setSelectedKeys(
                                e.target.value ? [e.target.value] : [],
                            );
                        }}
                        onPressEnter={() => confirm({ closeDropdown: false })}
                        style={{
                            width: 188,
                            marginBottom: 8,
                            display: 'block',
                        }}
                    />
                    <Space>
                        <Button
                            type="link"
                            onClick={() => {
                                clearFilters?.({
                                    confirm: true,
                                    closeDropdown: false,
                                });
                            }}
                            size="small"
                            style={{ width: 90 }}
                            disabled={!selectedKeys[0]}
                        >
                            重置
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => confirm()}
                            size="small"
                            style={{ width: 90 }}
                        >
                            确定
                        </Button>
                    </Space>
                </div>
            ),
            displayField: ['simple', 'full'],
        },
        {
            title: '木星通话ID',
            dataIndex: 'jupiterId',
            render: jupiterId => jupiterId || '-',
            width: 150,
            displayField: ['simple', 'full'],
        },
        {
            title: '发起系统',
            dataIndex: 'contactType',
            key: 'system',
            width: 100,
            render: contactType => {
                const system = reachSystemMap.get(contactType);

                return (
                    reachSystemoptions.find(it => it.value === system)?.label ||
                    '-'
                );
            },
        },
        {
            title: '发起人',
            dataIndex: ['staff', 'name'],
            key: 'creatorName',
            width: 150,
            render: (name: string, detail) => {
                return name
                    ? `${name || '-'}(${detail?.staff?.mis || '-'})`
                    : '-';
            },
        },
        {
            title: '触达类型',
            dataIndex: 'contactType',
            key: 'contactType',
            width: 100,
            render: (contactType: number) => {
                return (
                    <span>
                        {reachMethodOptions.find(
                            item => item.value === contactType,
                        )?.label || '-'}
                    </span>
                );
            },
        },
        {
            title: '商家类型',
            dataIndex: 'objectTypeName',
            key: 'merchantType',
            width: 100,
        },
        {
            title: '商家名称',
            dataIndex: 'objectName',
            key: 'objectName',
            width: 200,
            filterDropdown: ({
                setSelectedKeys,
                selectedKeys,
                confirm,
                clearFilters,
            }) => (
                <div style={{ padding: 8 }}>
                    <Input
                        placeholder="搜索商家名称/ID"
                        value={selectedKeys[0]}
                        onChange={e =>
                            setSelectedKeys(
                                e.target.value ? [e.target.value] : [],
                            )
                        }
                        onPressEnter={() => confirm()}
                        style={{
                            width: 188,
                            marginBottom: 8,
                            display: 'block',
                        }}
                    />
                    <Space>
                        <Button
                            onClick={() => {
                                clearFilters?.({
                                    confirm: true,
                                    closeDropdown: false,
                                });
                            }}
                            size="small"
                            style={{ width: 90 }}
                            type="link"
                            disabled={!selectedKeys[0]}
                        >
                            重置
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => confirm()}
                            size="small"
                            style={{ width: 90 }}
                        >
                            确定
                        </Button>
                    </Space>
                </div>
            ),
            render: (objectName: string, record: any) => {
                return objectName ? (
                    <div
                        style={{
                            maxWidth: 200,
                        }}
                    >
                        {`${objectName} (${record.objectId})`}
                    </div>
                ) : (
                    '/'
                );
            },
            displayField: ['simple', 'full'],
        },
        {
            title: '执行状态',
            dataIndex: 'executionStatus',
            key: 'executionStatus',
            render: (executionStatus: number) => {
                const status = executionStatusOptions.find(
                    item => item.value === executionStatus,
                );
                return <Tag color={status?.color}>{status?.label}</Tag>;
            },
            displayField: ['simple', 'full'],
        },
        {
            title: '触达结果',
            width: 110,
            dataIndex: 'reachStatus',
            filters: communicationResultOptions.map(item => ({
                text: item.label,
                value: item.value,
            })),
            key: 'reachStatus',
            render: (reachStatus: number, record: DataType) => {
                const status = communicationResultOptions.find(
                    item => item.value === reachStatus,
                );
                return status ? (
                    <Tag color={status?.color}>{status?.label}</Tag>
                ) : (
                    '/'
                );
            },
            displayField: ['simple', 'full'],
        },
        {
            title: '触达时间',
            dataIndex: 'startTime',
            key: 'contactTime',
            width: 200,
            render: (time: number, record: DataType) => {
                if (record.reachStatus === CommunicationResultEnum.FAIL) {
                    return record.submitTime
                        ? dayjs(record.submitTime).format('YYYY-MM-DD HH:mm:ss')
                        : '/';
                }
                return time
                    ? dayjs(time).format('YYYY-MM-DD HH:mm:ss')
                    : dayjs(record.submitTime).format('YYYY-MM-DD HH:mm:ss');
            },
            displayField: ['simple', 'full'],
        },
        {
            title: '沟通时长',
            dataIndex: 'talkingTimeLen',
            key: 'talkingTimeLen',
            render: (talkingTimeLen: number) => {
                return talkingTimeLen
                    ? `${Math.floor(talkingTimeLen / 1000 / 60)}分${
                          Math.floor(talkingTimeLen / 1000) % 60
                      }秒`
                    : '/';
            },
            displayField: ['simple', 'full'],
        },
        {
            title: '挂断人',
            dataIndex: 'releaseReason',
            key: 'releaseReason',
            render: reason => ReleaseReasonText[reason] || '-',
            displayField: ['simple', 'full'],
        },
        {
            title: (
                <>
                    沟通意向/失败原因
                    <Popover
                        title={null}
                        content={
                            '当前暂仅支持沟通意向筛选，失败原因筛选待支持。'
                        }
                        trigger="hover"
                    >
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                    </Popover>
                </>
            ),
            dataIndex: 'rankDesc',
            filters: rankFilters,
            key: 'rankDesc',
            render: (rankDesc: string, record: DataType) => {
                return (
                    <div className="flex-align-between">
                        <div style={{ maxWidth: '170px' }}>
                            {[record.rank, rankDesc]
                                .filter(Boolean)
                                .join('-') || '/'}
                        </div>
                        <Button
                            type="link"
                            onClick={() =>
                                fetchIntentionRecognize.run(record.contactId)
                            }
                            loading={fetchIntentionRecognize.loading}
                        >
                            重新输出意向
                        </Button>
                    </div>
                );
            },
            displayField: ['simple'],
        },
        {
            title: '客户意向',
            dataIndex: 'rankDesc',
            filters: rankFilters,
            key: 'rankDesc',
            render: (rankDesc: string, record: DataType) => {
                const showRank =
                    CommunicationResultEnum.SUCCESS === record.reachStatus;
                return showRank
                    ? [(record.rank, rankDesc)].filter(Boolean).join('-') || '/'
                    : '/';
            },
        },
        {
            title: '触达失败原因',
            dataIndex: 'releaseReasonMsg',
            key: 'releaseReasonMsg',
            width: 200,
            render: (releaseReasonMsg: string, record) => {
                const showReason =
                    CommunicationResultEnum.FAIL === record.reachStatus &&
                    releaseReasonMsg;

                return showReason ? (
                    <Tooltip title={releaseReasonMsg}>
                        <div
                            style={{
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                            }}
                        >
                            {releaseReasonMsg}
                        </div>
                    </Tooltip>
                ) : (
                    '/'
                );
            },
        },
        {
            title: '沟通总结',
            dataIndex: 'summarizing',
            width: 100,
            key: 'summarizing',
            render: (summarizing: string[]) => {
                return summarizing?.length ? (
                    <Tooltip title={summarizing.join(',')}>
                        <Typography.Link>查看总结</Typography.Link>
                    </Tooltip>
                ) : (
                    '/'
                );
            },
        },
        {
            title: '关联任务',
            dataIndex: 'taskName',
            key: 'taskName',
            render: (taskName: string, detail) => {
                return taskName ? (
                    <Tooltip title={`${taskName}(${detail.taskId})`}>
                        <div
                            style={{
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap',
                            }}
                        >
                            {taskName}({detail.taskId})
                        </div>
                    </Tooltip>
                ) : (
                    '/'
                );
            },
        },
        {
            title: '任务创建时间',
            dataIndex: 'submitTime',
            key: 'createTime',
            width: 178,
            render: (time: number) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '技能执行详情',
            dataIndex: 'skillExecutionList',
            key: 'skillExecutionList',
            width: 150,
            render: (
                skillExecutionList: {
                    skillName: string;
                    executed: boolean;
                }[],
            ) => {
                if (!skillExecutionList?.length) return '/';
                return (
                    <div>
                        {skillExecutionList.map((skill, index) => (
                            <div key={index}>
                                {skill.skillName}-
                                {skill.executed ? '执行成功' : '执行失败'}
                            </div>
                        ))}
                    </div>
                );
            },
            displayField: ['simple', 'full'],
        },
        {
            title: '触达内容',
            dataIndex: 'contactContent',
            key: 'contactContent',
            render: (contactContent: string) => {
                return contactContent ? (
                    <Tooltip title={contactContent}>
                        <Typography.Link>查看触达内容</Typography.Link>
                    </Tooltip>
                ) : (
                    '/'
                );
            },
        },
    ];

    const showColumns = columns
        .filter(item => {
            const displayField = item.displayField || ['full'];

            if (showPartColumns) {
                return displayField.includes('simple');
            }

            return displayField.includes('full');
        })
        .concat(operateColumn);

    return (
        <>
            <Table
                bordered
                key={[searchParams.taskId].join('-')}
                columns={showColumns}
                rowKey="contactId"
                scroll={{ x: 'max-content' }}
                rowSelection={
                    showPartColumns
                        ? undefined
                        : {
                              selectedRowKeys,
                              onChange: (selectedRowKeys: React.Key[]) => {
                                  setSelectedRowKeys(selectedRowKeys);
                                  onRowSelect?.(selectedRowKeys);
                              },
                          }
                }
                {...tableProps}
                pagination={{
                    ...tableProps.pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total: number) => `共 ${total} 条`,
                }}
            />

            <TaskReachDetail
                key={drawerParams.contactId}
                visible={drawerVisible}
                textPath={'/xianfu/api/dove/data/text'}
                onClose={() => {
                    setDrawerVisible(false);
                    setDrawerParams({});
                }}
                {...drawerParams}
            />
        </>
    );
};

export default TableList;
