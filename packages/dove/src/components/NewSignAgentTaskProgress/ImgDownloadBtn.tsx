import React, { useState } from 'react';
import { Button, message, Typography } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const { Text } = Typography;

// 批量下载图片组件
const ImgDownloadBtn = ({
    title,
    url,
}: {
    title: string;
    url: string | string[];
}) => {
    const [downloading, setDownloading] = useState(false);

    const handleUrls = () => {
        return Array.isArray(url) ? url.filter(Boolean) : [url].filter(Boolean);
    };

    const handleDownload = async () => {
        const urls = handleUrls();
        if (urls.length === 0) {
            message.warning('没有可下载的图片');
            return;
        }

        setDownloading(true);

        try {
            if (urls.length === 1) {
                // 单个图片下载
                await downloadImage(urls[0], `${title}_${Date.now()}`);
                message.success('图片下载完成');
            } else {
                // 批量下载多个图片
                message.info(`开始下载 ${urls.length} 张图片...`);
                // 顺序下载，避免浏览器阻止多个下载
                for (let i = 0; i < urls.length; i++) {
                    await new Promise(resolve => setTimeout(resolve, 300));
                    await downloadImage(
                        urls[i],
                        `${title}_${Date.now()}_${i + 1}`,
                    );
                }
                message.success(`${urls.length} 张图片下载完成`);
            }
        } catch (error) {
            message.error('下载失败，请重试');
            console.error('Download error:', error);
        } finally {
            setDownloading(false);
        }
    };

    const downloadImage = async (imageUrl: string, fileName: string) => {
        try {
            // 使用fetch API获取图片数据
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理创建的对象URL
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Download image error:', error);
            throw error;
        }
    };

    const urls = handleUrls();
    const downloadText = urls.length > 1 ? '批量下载' : '下载';

    return (
        <div>
            <Text type="secondary">{title}：</Text>
            <Button
                type="link"
                size="small"
                className="download-btn"
                onClick={handleDownload}
                disabled={urls.length === 0}
                loading={downloading}
                icon={!downloading ? <DownloadOutlined /> : undefined}
            >
                {downloading ? '下载中...' : downloadText}
            </Button>
        </div>
    );
};

export default ImgDownloadBtn;
