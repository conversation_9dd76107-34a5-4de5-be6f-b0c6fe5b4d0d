import React, { FC, useEffect } from 'react';
import { Drawer, Timeline, Tag, Typography, Button, message } from 'antd';
import {
    CheckCircleFilled,
    ClockCircleFilled,
    CloseCircleFilled,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useRequest, useUpdateEffect } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import ImgDownloadBtn from './ImgDownloadBtn';
import './index.scss';

const { Text, Paragraph } = Typography;

interface TaskProgressItem {
    agentSubTaskType: string;
    executeStatusDesc: string;
    executeStatusValue: number;
    executeTime?: number;
    extInfo: Record<string, any>;
}

type agentTaskInfoType = {
    poiId?: number; // 门店id
    poiName?: string; // 门店名称
    poiAddress?: string; // 门店地址
    poiCategory?: string; // 门店品类
    poiPhone?: string; // 门店电话
    poiLocation?: string; // 门店位置
    agentConcatId?: number; //agent沟通任务id
};

interface TaskProgressProps {
    visible: boolean;
    onClose: () => void;
    /** 线索id */
    leadId?: string;
    /** wdc门店id */
    wdcId: string;
    /** 操作列表 */
    actions?: string[];
    /** 操作信息 */
    agentTaskInfo?: agentTaskInfoType;
}

// 状态映射配置
const agentStatusConfig = {
    30: {
        color: '#00BF7F',
        backgroundColor: 'rgb(216, 240, 240)',
        icon: <CheckCircleFilled />,
    },
    40: {
        color: '#FF192D',
        backgroundColor: 'rgb(255, 230, 231)',
        icon: <CloseCircleFilled />,
    },
    10: {
        color: '#FF6A00',
        backgroundColor: 'rgb(246, 231, 226)',
        icon: <ClockCircleFilled />,
    },
    20: {
        color: '#198CFF ',
        backgroundColor: 'rgb(226, 236, 255)',
        icon: <ClockCircleFilled />,
    },
};

// 任务类型中文映射
const taskTypeMap: Record<string, string> = {
    外呼意向嗅探: '外呼意向嗅探',
    加企微: '加企微',
    注册手机号收集: '获取注册手机号',
    入驻方式确认: '商家账号注册',
    联系人信息索要: '获取联系人信息',
    门脸图收集: '获取门脸图',
    营业执照收集: '获取营业执照',
    位置信息收集: '获取位置',
    食品经营许可证收集: '获取食品经营许可证',
    身份证收集: '获取身份证',
    三项送审: '三项送审',
    降级人工: '降级人工',
};

// 渲染不同任务类型的详情内容
const renderTaskDetail = (item: TaskProgressItem) => {
    const { agentSubTaskType, extInfo } = item;

    switch (agentSubTaskType) {
        case '外呼意向嗅探':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">客户意向：</Text>
                        <Text>{extInfo.customerIntention || '-'}</Text>
                    </div>
                </div>
            );
        case '加企微':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">添加结果：</Text>
                        <Text>{extInfo.addWechatTime ? '添加成功' : '-'}</Text>
                    </div>
                </div>
            );
        case '注册手机号收集':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">手机号：</Text>
                        <Text>{extInfo.contactPhone || '-'}</Text>
                        {extInfo.contactPhone && (
                            <Button
                                type="link"
                                size="small"
                                className="view-btn"
                            >
                                查看
                            </Button>
                        )}
                    </div>
                    <div className="detail-item">
                        <Text type="secondary">是否有效：</Text>
                        <Text>{extInfo.checkContactPhoneValid || '-'}</Text>
                    </div>
                    {extInfo.checkContactPhoneFailReason && (
                        <div className="detail-item">
                            <Text type="secondary">失败原因：</Text>
                            <Text>{extInfo.checkContactPhoneFailReason}</Text>
                        </div>
                    )}
                </div>
            );
        case '入驻方式确认':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">商家账号：</Text>
                        <Text>{extInfo.merchantAccountId || '-'}</Text>
                    </div>
                    <div className="detail-item">
                        <Text type="secondary">入驻方式：</Text>
                        <Text>{extInfo.storeEntryWay || '-'}</Text>
                    </div>
                </div>
            );
        case '联系人信息索要':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">手机号：</Text>
                        <Text>{extInfo.contactPhone || '-'}</Text>
                        {extInfo.contactPhone && (
                            <Button
                                type="link"
                                size="small"
                                className="view-btn"
                            >
                                查看
                            </Button>
                        )}
                    </div>
                    <div className="detail-item">
                        <Text type="secondary">联系人姓名：</Text>
                        <Text>{extInfo.contactName || '-'}</Text>
                    </div>
                </div>
            );
        case '门脸图收集':
            return (
                <div className="task-detail images">
                    <div className="detail-item">
                        {extInfo.shopFrontImage ? (
                            <div className="image-preview">
                                <img
                                    src={extInfo.shopFrontImage}
                                    alt="门脸照片"
                                />
                                <ImgDownloadBtn
                                    title="门脸照片"
                                    url={[
                                        extInfo.shopFrontImage,
                                        extInfo.halalLicenseImage,
                                    ]}
                                />
                            </div>
                        ) : (
                            '-'
                        )}
                    </div>
                    {extInfo.halalLicenseImage && (
                        <div className="detail-item">
                            <div className="image-preview">
                                <img
                                    src={extInfo.halalLicenseImage}
                                    alt="清真许可证"
                                />
                            </div>
                        </div>
                    )}
                </div>
            );
        case '营业执照收集':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        {extInfo.businessLicenseImage ? (
                            <div className="image-preview">
                                <img
                                    src={extInfo.businessLicenseImage}
                                    alt="营业执照"
                                />
                                <ImgDownloadBtn
                                    title="营业执照"
                                    url={extInfo.businessLicenseImage}
                                />
                            </div>
                        ) : (
                            '-'
                        )}
                    </div>
                </div>
            );
        case '位置信息收集':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">经纬度：</Text>
                        <Text>
                            {extInfo.longitude && extInfo.latitude
                                ? `${extInfo.longitude}, ${extInfo.latitude}`
                                : '-'}
                        </Text>
                    </div>
                    <div className="detail-item">
                        <Text type="secondary">地址：</Text>
                        <Text>{extInfo.address || '-'}</Text>
                    </div>
                </div>
            );
        case '食品经营许可证收集':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        {extInfo.foodBusinessLicenseImage ? (
                            <div className="image-preview">
                                <img
                                    src={extInfo.foodBusinessLicenseImage}
                                    alt="食品经营许可证"
                                />
                                <ImgDownloadBtn
                                    title="食品经营许可证"
                                    url={extInfo.foodBusinessLicenseImage}
                                />
                            </div>
                        ) : (
                            '-'
                        )}
                    </div>
                </div>
            );
        case '身份证收集':
            return (
                <div className="task-detail images">
                    <div className="detail-item">
                        {extInfo.idCardFrontImage ? (
                            <div className="image-preview">
                                <img
                                    src={extInfo.idCardFrontImage}
                                    alt="身份证正面"
                                />
                                <ImgDownloadBtn
                                    title="身份证正面"
                                    url={[
                                        extInfo.idCardFrontImage,
                                        extInfo.idCardBackImage,
                                    ]}
                                />
                            </div>
                        ) : (
                            '-'
                        )}
                    </div>
                    <div className="detail-item">
                        {extInfo.idCardBackImage ? (
                            <div className="image-preview">
                                <img
                                    src={extInfo.idCardBackImage}
                                    alt="身份证反面"
                                />
                            </div>
                        ) : (
                            '-'
                        )}
                    </div>
                </div>
            );
        case '三项送审':
            return (
                <div className="task-detail">
                    {extInfo.rejectTime && (
                        <div className="detail-item">
                            <Text type="secondary">驳回时间：</Text>
                            <Text>
                                {dayjs(extInfo.rejectTime * 1000).format(
                                    'YYYY-MM-DD HH:mm:ss',
                                )}
                            </Text>
                        </div>
                    )}
                    {extInfo.rejectReason && (
                        <div className="detail-item">
                            <Text type="secondary">驳回原因：</Text>
                            <Text>{extInfo.rejectReason}</Text>
                        </div>
                    )}
                    {extInfo.passTime && (
                        <div className="detail-item">
                            <Text type="secondary">通过时间：</Text>
                            <Text>
                                {dayjs(extInfo.passTime * 1000).format(
                                    'YYYY-MM-DD HH:mm:ss',
                                )}
                            </Text>
                        </div>
                    )}
                </div>
            );
        case '降级人工':
            return (
                <div className="task-detail">
                    <div className="detail-item">
                        <Text type="secondary">降级原因：</Text>
                        <Text>{extInfo.downgradeReason || '-'}</Text>
                    </div>
                    {extInfo.downgradeStaff && (
                        <div className="detail-item">
                            <Text type="secondary">降级人员：</Text>
                            <Text>{`${extInfo.downgradeStaff.name || '-'}(${
                                extInfo.downgradeStaff.mis || '-'
                            })`}</Text>
                        </div>
                    )}
                </div>
            );
        default:
            return null;
    }
};

const NewSignAgentTaskProgress: FC<TaskProgressProps> = ({
    visible,
    onClose,
    wdcId,
    actions = [
        'Communicate', //agent沟通记录详情
        'LeadDetail', //线索详情
        'HighseasDetail', //公海商家详情
    ],
    agentTaskInfo = {}, // 任务信息
}) => {
    const getLeadId = async () => {
        try {
            const res = await apiCaller.post(
                '/xianfu/api/clue/newSignLead/agent/subTaskQuery',
                {
                    wdcId,
                },
            );
            if (res.code === 0) {
                return res.data;
            }
            message.error('获取线索id失败');
        } catch (error) {
            message.error('服务异常，请重试');
            console.log(error);
        }
        return null;
    };

    const {
        data,
        loading,
        run: getTaskProgress,
    } = useRequest(async () => {
        try {
            // const leadId = await getLeadId();
            // if (!leadId) return null;
            // const res = await apiCaller.post(
            //     '/xianfu/api/clue/newSignLead/agent/subTaskQuery',
            //     {
            //         leadId,
            //     },
            // );
            const res = {
                code: 0,
                msg: '成功',
                data: {
                    code: 0,
                    msg: '成功',
                    data: [
                        {
                            agentSubTaskType: '外呼意向嗅探',
                            executeStatusDesc: '已完成',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                customerIntention: '有意向',
                            },
                        },
                        {
                            agentSubTaskType: '加企微',
                            executeStatusDesc: '等待回复',
                            executeStatusValue: 20,
                            extInfo: {
                                addWechatTime: **********,
                            },
                        },
                        {
                            agentSubTaskType: '注册手机号收集',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                contactPhone: '135****1234',
                                checkContactPhoneValid: '是',
                                checkContactPhoneFailReason: '已经注册',
                            },
                        },
                        {
                            agentSubTaskType: '入驻方式确认',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                merchantAccountId: 'mud12820111',
                                storeEntryWay: '自入驻',
                            },
                        },
                        {
                            agentSubTaskType: '联系人信息索要',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                contactPhone: '135****1234',
                                contactName: '李华',
                            },
                        },
                        {
                            agentSubTaskType: '门脸图收集',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                shopFrontImage:
                                    'https://s3plus.meituan.net/prototype-center/AI-Agent/%E9%97%A8%E5%BA%97%E5%9B%BE%E7%89%87.png',
                                halalLicenseImage:
                                    'https://s3plus.meituan.net/prototype-center/AI-Agent/%E9%97%A8%E5%BA%97%E5%9B%BE%E7%89%87.png',
                            },
                        },
                        {
                            agentSubTaskType: '营业执照收集',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                businessLicenseImage:
                                    'https://s3plus.meituan.net/prototype-center/AI-Agent/%E9%97%A8%E5%BA%97%E6%89%A7%E7%85%A7.png',
                            },
                        },
                        {
                            agentSubTaskType: '位置信息收集',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                address: '广顺北大街',
                                longitude: '12.342',
                                latitude: '123.123',
                            },
                        },
                        {
                            agentSubTaskType: '食品经营许可证收集',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                foodBusinessLicenseImage:
                                    'https://s3plus.meituan.net/prototype-center/AI-Agent/%E9%97%A8%E5%BA%97%E6%89%A7%E7%85%A7.png',
                            },
                        },
                        {
                            agentSubTaskType: '身份证收集',
                            executeStatusDesc: '收集成功',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                idCardFrontImage:
                                    'https://s3plus.meituan.net/prototype-center/AI-Agent/%E9%97%A8%E5%BA%97%E6%89%A7%E7%85%A7.png',
                                idCardBackImage:
                                    'https://s3plus.meituan.net/prototype-center/AI-Agent/%E9%97%A8%E5%BA%97%E6%89%A7%E7%85%A7.png',
                            },
                        },
                        {
                            agentSubTaskType: '统一提审',
                            executeStatusDesc: '已通过',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                rejectTime: **********,
                                rejectReason: '失败',
                                passTime: **********,
                            },
                        },
                        {
                            agentSubTaskType: '降级人工',
                            executeStatusDesc: '已完成',
                            executeStatusValue: 30,
                            executeTime: **********,
                            extInfo: {
                                downgradeReason: '失败',
                                downgradeStaff: {
                                    name: '李华',
                                    mis: 'lihua',
                                },
                            },
                        },
                    ],
                },
            };
            if (res?.code === 0 && res?.data?.code === 0 && res?.data?.data) {
                return res.data.data as TaskProgressItem[];
            }
        } catch (error) {
            console.log(error);
        }
        return [];
    });

    useUpdateEffect(() => {
        getTaskProgress();
    }, [wdcId]);

    // 格式化时间戳为可读时间
    const formatTime = (timestamp?: number) => {
        if (!timestamp) return '-';
        return dayjs(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss');
    };

    // 获取状态配置
    const getStatusConfig = (status: number) => {
        return agentStatusConfig[status] || { color: '#d9d9d9', icon: null };
    };

    const ActionList = [
        {
            name: 'Communicate',
            title: '沟通记录',
            onclick: () => {},
        },
        {
            name: 'LeadDetail',
            title: '线索详情',
            onclick: () => {},
        },
        {
            name: 'HighseasDetail',
            title: '商家详情',
            onclick: () => {},
        },
    ];

    return (
        <Drawer
            title="AI新签进度查看"
            placement="right"
            width={900}
            onClose={onClose}
            open={visible}
            className="task-progress-drawer"
            loading={loading}
            
        >
            <Timeline className="task-timeline">
                {data?.map((item, index) => {
                    const { agentSubTaskType, executeTime } = item;
                    const { icon, ...style } = getStatusConfig(
                        item?.executeStatusValue,
                    );
                    const taskName =
                        taskTypeMap[agentSubTaskType] || agentSubTaskType;

                    return (
                        <Timeline.Item
                            key={`${agentSubTaskType}-${index}`}
                            color={style.color}
                            dot={icon}
                        >
                            <div className="timeline-item">
                                <div className="timeline-header">
                                    <Text strong>{taskName}</Text>
                                    <Text type="secondary">
                                        {formatTime(executeTime)}
                                    </Text>
                                </div>
                                <Tag
                                    style={{
                                        ...style,
                                        border: 'none',
                                        opacity: 0.8,
                                    }}
                                >
                                    {item?.executeStatusDesc}
                                </Tag>
                                {renderTaskDetail(item)}
                            </div>
                        </Timeline.Item>
                    );
                })}
            </Timeline>
        </Drawer>
    );
};

export default NewSignAgentTaskProgress;
