.task-progress-drawer {
    .ant-timeline-item-tail {
        border: 0.5px dashed #999;
        inset-block-start: 18px;
        height: calc(100% - 26px);
    }

    .ant-drawer-body {
        padding: 16px 24px;
    }

    .task-timeline {
        padding-top: 8px;
        margin-left: 200px;

        .timeline-item {
            margin-bottom: 16px;

            .timeline-header {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                margin-bottom: 8px;
                position: absolute;
                left: -200px;
            }

            .task-detail {
                margin-top: 8px;

                .detail-item {
                    display: flex;
                    align-items: flex-start;
                    margin-bottom: 8px;

                    &:last-child {
                        margin-bottom: 0;
                    }

                    .ant-typography {
                        &.ant-typography-secondary {
                            min-width: 80px;
                            flex-shrink: 0;
                        }
                    }

                    .view-btn {
                        padding: 0 4px;
                        height: auto;
                        line-height: 1.5;
                        margin-left: 8px;
                    }

                    .image-preview {
                        display: flex;
                        flex-direction: column;
                        align-items: flex-start;

                        img {
                            max-width: 200px;
                            max-height: 120px;
                            border-radius: 4px;
                            border: 1px solid #d9d9d9;
                            margin-bottom: 8px;
                        }

                        .download-btn {
                            padding: 0;
                            height: auto;
                            line-height: 1.5;
                        }
                    }
                }
            }

            .task-detail.images {
                display: flex;
                gap: 8px 12px;
            }
        }
    }

    // 状态图标样式
    .ant-timeline-item-head-custom {
        background: none;

        .anticon {
            font-size: 16px;
        }
    }

    // 已完成状态
    .ant-tag-green {
        background-color: #f6ffed;
        border-color: #b7eb8f;
        color: #52c41a;
    }

    // 失败状态
    .ant-tag-red {
        background-color: #fff1f0;
        border-color: #ffa39e;
        color: #f5222d;
    }

    // 等待状态
    .ant-tag-gold {
        background-color: #fffbe6;
        border-color: #ffe58f;
        color: #faad14;
    }
}