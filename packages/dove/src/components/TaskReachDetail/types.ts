export type TaskReachDetailProps = {
    visible: boolean;
    onClose: () => void;
    audioUrl?: string;
    contactId?: string;
    textPath?: string;
    contactType: number;
    talkingSeconds?: number;
    contactContent?: string;
    contactImages?: string[];
    massSendTitle?: string;
};

export type TaskReachDetailParams = {
    audioUrl: string; //沟通链接
    contactId: string; // 任务ID
    talkingSeconds?: number;
    contactContent?: string;
    contactImages?: string[];
    massSendTitle?: string;
    contactType: number; //触达方式
};
